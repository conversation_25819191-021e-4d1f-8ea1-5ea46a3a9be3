import gradio as gr
from faster_whisper import WhisperModel
import os
import traceback
import tempfile
import datetime
import time
import configparser  # 新增导入

# 固定模型路径，从配置文件读取
config = configparser.ConfigParser()
conf_path = os.path.join(os.path.dirname(__file__), "model.conf")
config.read(conf_path, encoding="utf-8")
MODELS_BASE_DIR = config["faster_whisper_dir"]["faster_whisper_models_root"]

def get_local_models(base_dir):
    """扫描指定基础目录下的子目录作为本地模型列表。"""
    if not base_dir or not os.path.isdir(base_dir):
        return []
    try:
        models = [d for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
        return sorted(models)  # 排序以确保一致的显示顺序
    except Exception as e:
        return []

# 初始化模型列表
MODEL_SIZES = get_local_models(MODELS_BASE_DIR)
DEFAULT_MODEL_SIZE = None
if MODEL_SIZES:
    if "tiny" in MODEL_SIZES:  # 优先选择 'tiny' 作为默认
        DEFAULT_MODEL_SIZE = "tiny"
    else:
        DEFAULT_MODEL_SIZE = MODEL_SIZES[0]  # 否则选择列表中的第一个

# --- 辅助函数 --- #
def format_srt_timestamp(seconds_float: float) -> str:
    """将秒数（浮点数）转换为 HH:MM:SS,mmm SRT时间戳格式。"""
    delta = datetime.timedelta(seconds=seconds_float)
    hours, remainder = divmod(delta.seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    milliseconds = int(delta.microseconds / 1000)
    return f"{hours:02}:{minutes:02}:{seconds:02},{milliseconds:03}"

def generate_txt_content(segments_iterable):
    """从Whisper句段的可迭代对象生成纯文本内容。"""
    return "\n".join(segment.text.strip() for segment in segments_iterable if segment.text)

def generate_srt_content(segments_iterable):
    """从Whisper句段的可迭代对象生成SRT内容。"""
    srt_content = []
    for i, segment in enumerate(segments_iterable):
        start_time = format_srt_timestamp(segment.start)
        end_time = format_srt_timestamp(segment.end)
        srt_content.append(str(i + 1))
        srt_content.append(f"{start_time} --> {end_time}")
        srt_content.append(segment.text.strip() if segment.text else "")
        srt_content.append("")  # 空行分隔
    return "\n".join(srt_content)

def save_temp_file(content: str, extension: str, original_audio_path: str) -> str:
    """将内容保存到临时文件并返回其路径。文件名将基于原始音频文件名。"""
    if not original_audio_path or not isinstance(original_audio_path, str):
        try:
            with tempfile.NamedTemporaryFile(mode="w", suffix=extension, prefix="transcription_", delete=False, encoding='utf-8') as tmp_file:
                tmp_file.write(content)
                return tmp_file.name
        except Exception as e:
            print(f"保存通用临时文件时出错 ({extension}): {e}")
            return None

    base_name = os.path.splitext(os.path.basename(original_audio_path))[0]
    desired_filename = f"{base_name}{extension}"
    temp_dir = tempfile.gettempdir()
    full_temp_path = os.path.join(temp_dir, desired_filename)

    try:
        with open(full_temp_path, "w", encoding='utf-8') as tmp_file:
            tmp_file.write(content)
        return full_temp_path
    except Exception as e:
        print(f"保存定制名称的临时文件时出错 '{full_temp_path}': {e}")
        try:
            print(f"回退到使用随机名称的临时文件保存 ({extension})")
            with tempfile.NamedTemporaryFile(mode="w", suffix=extension, prefix=f"{base_name}_", delete=False, encoding='utf-8') as tmp_file_fallback:
                tmp_file_fallback.write(content)
                return tmp_file_fallback.name
        except Exception as e_fallback:
            print(f"回退保存临时文件也失败 ({extension}): {e_fallback}")
            return None

# --- 词语时间戳处理函数 --- #
def transcribe_word_timestamps(audio_file_path, selected_model_name, device_type, compute_type_selected, language_choice):
    duration = None
    if audio_file_path is None:
        return "请上传一个音频文件。", None, None, None, duration
    if selected_model_name is None:
        return "请选择一个模型大小。", None, None, None, duration
    
    model = None
    try:
        actual_model_path = os.path.join(MODELS_BASE_DIR, selected_model_name)
        if not os.path.isdir(actual_model_path):
            return f"错误: 模型路径 '{actual_model_path}' 无效。", None, None, None, duration

        print(f"词语时间戳 - 加载模型路径: {actual_model_path}, 设备: {device_type}, 计算类型: {compute_type_selected}, 语言: {language_choice}")
        model = WhisperModel(actual_model_path, device=device_type, compute_type=compute_type_selected)
        
        transcribe_lang = None
        if language_choice == "中文 (zh)":
            transcribe_lang = "zh"
        elif language_choice == "英文 (en)":
            transcribe_lang = "en"
        
        start_time = time.time()
        segments_generator, info = model.transcribe(audio_file_path, word_timestamps=True, language=transcribe_lang)
        segments_list = list(segments_generator)
        end_time = time.time()
        duration = end_time - start_time
        
        full_text_parts = []
        word_data = []
        
        for segment_idx, segment in enumerate(segments_list):
            full_text_parts.append(segment.text)
            if segment.words:
                for word in segment.words:
                    word_data.append({
                        "词语": word.word,
                        "开始 (s)": f"{word.start:.3f}",
                        "结束 (s)": f"{word.end:.3f}",
                        "置信度": f"{word.probability:.3f}",
                        "句段ID": segment_idx
                    })
        
        full_text = "".join(full_text_parts)
        
        df_data_output = []
        for item in word_data:
            df_data_output.append([
                item["词语"],
                item["开始 (s)"],
                item["结束 (s)"],
                item["置信度"],
                item["句段ID"]
            ])

        txt_file_path = None
        srt_file_path = None
        if audio_file_path and segments_list:
            txt_file_path = save_temp_file(full_text, ".txt", audio_file_path)
            srt_content = generate_srt_content(segments_list)
            srt_file_path = save_temp_file(srt_content, ".srt", audio_file_path)
            
        return full_text, df_data_output, txt_file_path, srt_file_path, duration
    except Exception as e:
        print(traceback.format_exc())
        return f"错误: {str(e)}", None, None, None, duration
    finally:
        if model is not None:
            del model
        if device_type == "cuda":
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except ImportError:
                print("PyTorch not found, cannot empty CUDA cache for word_timestamps.")
            except Exception as e_cuda:
                print(f"Error clearing CUDA cache for word_timestamps: {e_cuda}")

# --- 句级时间戳处理函数 --- #
def transcribe_segment_timestamps(audio_file_path, selected_model_name, device_type, compute_type_selected, language_choice):
    duration = None
    if audio_file_path is None:
        return "请上传一个音频文件。", None, None, None, duration
    if selected_model_name is None:
        return "请选择一个模型大小。", None, None, None, duration
    
    model = None
    try:
        actual_model_path = os.path.join(MODELS_BASE_DIR, selected_model_name)
        if not os.path.isdir(actual_model_path):
            return f"错误: 模型路径 '{actual_model_path}' 无效。", None, None, None, duration

        print(f"句级时间戳 - 加载模型路径: {actual_model_path}, 设备: {device_type}, 计算类型: {compute_type_selected}, 语言: {language_choice}")
        model = WhisperModel(actual_model_path, device=device_type, compute_type=compute_type_selected)
        
        transcribe_lang = None
        if language_choice == "中文 (zh)":
            transcribe_lang = "zh"
        elif language_choice == "英文 (en)":
            transcribe_lang = "en"
            
        start_time = time.time()
        segments_generator, info = model.transcribe(audio_file_path, language=transcribe_lang)
        segments_list = list(segments_generator)
        end_time = time.time()
        duration = end_time - start_time
        
        full_text_parts = []
        segment_data_for_df = []
        
        for segment_idx, segment in enumerate(segments_list):
            full_text_parts.append(segment.text)
            segment_data_for_df.append({
                "ID": segment_idx,
                "开始 (s)": f"{segment.start:.3f}",
                "结束 (s)": f"{segment.end:.3f}",
                "文本": segment.text
            })
        
        full_text = "".join(full_text_parts)
        
        df_data_output = []
        for item in segment_data_for_df:
            df_data_output.append([
                item["ID"],
                item["开始 (s)"],
                item["结束 (s)"],
                item["文本"]
            ])

        txt_file_path = None
        srt_file_path = None
        if audio_file_path and segments_list:
            txt_file_path = save_temp_file(full_text, ".txt", audio_file_path)
            srt_content = generate_srt_content(segments_list)
            srt_file_path = save_temp_file(srt_content, ".srt", audio_file_path)
            
        return full_text, df_data_output, txt_file_path, srt_file_path, duration
    except Exception as e:
        print(traceback.format_exc())
        return f"错误: {str(e)}", None, None, None, duration
    finally:
        if model is not None:
            del model
        if device_type == "cuda":
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except ImportError:
                print("PyTorch not found, cannot empty CUDA cache for segment_timestamps.")
            except Exception as e_cuda:
                print(f"Error clearing CUDA cache for segment_timestamps: {e_cuda}")

# --- Gradio UI 处理函数 --- #
def format_transcription_outputs(full_text, df_data, txt_file_path, srt_file_path, duration):
    label_update = "转录结果"
    if duration is not None:
        label_update = f"转录结果 (耗时 {duration:.2f} 秒)"
    
    text_output = full_text if full_text is not None else ""
    
    return gr.update(value=text_output, label=label_update), df_data, txt_file_path, srt_file_path

# --- Gradio UI --- #
with gr.Blocks(title="Faster-Whisper 时间戳提取工具") as demo:
    gr.Markdown(f"""
    # Faster-Whisper 时间戳提取
    使用此界面从音频中提取词语级和句级时间戳，并下载TXT或SRT格式的转录结果。
    
    **模型目录**: `{MODELS_BASE_DIR}`
    """)

    with gr.Row():
        global_device_type = gr.Radio(["cpu", "cuda"], value="cpu", label="选择设备", info="若选择cuda,请确保CUDA环境可用")
        global_compute_type = gr.Dropdown(
            choices=["default", "int8", "float16", "int8_float16", "int16", "float32"],
            value="default", 
            label="计算类型 (Compute Type)",
            info="'default' 通常对CPU是int8, 对CUDA是float16. 具体请查阅faster-whisper文档."
        )
        global_language_select = gr.Dropdown(
            choices=["自动检测", "中文 (zh)", "英文 (en)"], 
            value="自动检测", 
            label="转录语言 (Transcription Language)",
            info="选择特定语言可以提高准确性并加快处理速度。"
        )

    with gr.Tabs():
        with gr.TabItem("词语时间戳"):
            gr.Markdown("获取转录文本中每个词语的起止时间戳。")
            with gr.Row():
                audio_input_ts = gr.Audio(type="filepath", label="上传音频")
                model_select_ts = gr.Dropdown(MODEL_SIZES, value=DEFAULT_MODEL_SIZE, label="选择模型大小")
            transcribe_button_ts = gr.Button("转录并获取词语时间戳")
            output_text_ts = gr.Textbox(label="转录结果", lines=5, show_copy_button=True)
            output_word_ts_df = gr.DataFrame(
                label="词语时间戳", 
                headers=["词语", "开始 (s)", "结束 (s)", "置信度", "句段ID"]
            )
            with gr.Row():
                output_txt_file_ts = gr.File(label="下载 TXT 文件")
                output_srt_file_ts = gr.File(label="下载 SRT 文件")

        with gr.TabItem("句级时间戳"):
            gr.Markdown("获取转录文本中每个语音句段（句子或短语）的起止时间戳和内容。")
            with gr.Row():
                audio_input_seg_ts = gr.Audio(type="filepath", label="上传音频")
                model_select_seg_ts = gr.Dropdown(MODEL_SIZES, value=DEFAULT_MODEL_SIZE, label="选择模型大小")
            transcribe_button_seg_ts = gr.Button("转录并获取句级时间戳")
            output_text_seg_ts = gr.Textbox(label="转录结果", lines=5, show_copy_button=True)
            output_segment_ts_df = gr.DataFrame(
                label="句级时间戳", 
                headers=["ID", "开始 (s)", "结束 (s)", "文本"]
            )
            with gr.Row():
                output_txt_file_seg_ts = gr.File(label="下载 TXT 文件")
                output_srt_file_seg_ts = gr.File(label="下载 SRT 文件")

    # 转录按钮事件
    transcribe_button_ts.click(
        lambda audio, model, dev, comp, lang: format_transcription_outputs(*transcribe_word_timestamps(audio, model, dev, comp, lang)),
        inputs=[audio_input_ts, model_select_ts, global_device_type, global_compute_type, global_language_select],
        outputs=[output_text_ts, output_word_ts_df, output_txt_file_ts, output_srt_file_ts]
    )

    transcribe_button_seg_ts.click(
        lambda audio, model, dev, comp, lang: format_transcription_outputs(*transcribe_segment_timestamps(audio, model, dev, comp, lang)),
        inputs=[audio_input_seg_ts, model_select_seg_ts, global_device_type, global_compute_type, global_language_select],
        outputs=[output_text_seg_ts, output_segment_ts_df, output_txt_file_seg_ts, output_srt_file_seg_ts]
    )

if __name__ == "__main__":
    demo.launch(server_port=7860, server_name="127.0.0.1", inbrowser=True)
