# 统一语音识别GUI客户端使用说明

## 概述

基于原有的 `app/gui_asr.py` 进行了重大升级和优化，现在支持三种不同的语音识别模型类型，通过统一的界面和配置文件进行操作。

## 代码优化

### 🎯 配置文件统一
- 使用统一的 `app/model.conf` 配置文件
- 根据模型类型自动选择对应的配置section：
  - `[asr_models_dir]` - Paraformer ASR模型
  - `[asr_seaco_models_dir]` - Seaco Paraformer ASR模型  
  - `[asr_sense_model_dir]` - SenseVoice ASR模型
  - `[vad_models_dir]` - VAD模型 (通用)
  - `[punc_models_dir]` - 标点模型 (通用)
  - `[spk_models_dir]` - 说话人模型 (通用)

### 🔧 代码结构优化
- 移除了冗余的配置文件路径判断
- 简化了界面更新逻辑，使用配置字典
- 移除了重复的import语句
- 优化了模型加载和缓存机制

## 主要功能

### 🎯 模型类型选择
- **Paraformer-zh-spk**: 标准四模型组合 (ASR+VAD+PUNC+SPK)
- **Seaco Paraformer**: 四模型组合 + 热词功能 + VAD优化
- **SenseVoice**: 双模型组合 (ASR+VAD) + 情感/事件识别

### 🔧 动态界面适配
- 根据选择的模型类型自动显示/隐藏相关功能
- 智能的模型配置加载
- 统一的结果处理逻辑

## 配置文件结构

`app/model.conf` 文件结构示例：

```ini
[asr_models_dir]
speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch = /path/to/paraformer/model

[asr_seaco_models_dir]  
speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch = /path/to/seaco/model

[asr_sense_model_dir]
sensevoice_small = /path/to/sensevoice/model

[vad_models_dir]
speech_fsmn_vad_zh-cn-16k-common-pytorch = /path/to/vad/model

[punc_models_dir]
punc_ct-transformer_zh-cn-common-vocab272727-pytorch = /path/to/punc/model

[spk_models_dir]
speech_campplus_sv_zh-cn_16k-common = /path/to/spk/model
```

## 使用步骤

### 1. 启动应用
```bash
cd app
python gui_asr.py
```

### 2. 选择模型类型
在界面顶部的"模型类型"区域选择：
- **Paraformer-zh-spk**: 适用于一般的中文语音识别
- **Seaco Paraformer**: 需要使用热词功能时选择
- **SenseVoice**: 需要情感和事件检测时选择

### 3. 配置参数
- **音频文件**: 点击"浏览文件"选择音频
- **设备**: 选择 CPU 或 CUDA
- **模型配置**: 界面会根据模型类型自动加载对应的模型选项
- **特殊功能**:
  - Seaco: 在"热词"框中输入自定义词汇
  - Paraformer/Seaco: 设置说话人数量
- **输出格式**: 选择是否输出 SRT 格式

### 4. 开始识别
点击"开始识别"按钮，查看右侧面板的实时进度和结果。

## 功能特性

### 通用功能
- ✅ 音频文件选择 (WAV, MP3, FLAC, M4A, PCM)
- ✅ CPU/CUDA 设备选择
- ✅ SRT 格式输出
- ✅ 模型缓存机制
- ✅ 异步处理
- ✅ 实时进度显示
- ✅ 错误处理和用户提示

### 模型特定功能

#### Paraformer-zh-spk
- 📊 说话人数量设置 (0=自动检测)
- 🏷️ 说话人标签 ([spk1], [spk2], ...)
- ⏰ 精确时间戳

#### Seaco Paraformer  
- 📊 说话人数量设置
- 🔤 **热词输入** (空格分隔)
- 🏷️ 说话人标签
- ⏰ 精确时间戳
- 🔧 VAD 优化参数 (max_single_segment_time: 60000)

#### SenseVoice
- 😊 **情感识别** (HAPPY, SAD, ANGRY, NEUTRAL, ...)
- 🎵 **事件检测** (Speech, BGM, Applause, Laughter, ...)
- ⏰ 时间戳支持
- 🌐 语言自动检测
- 🔧 VAD 优化参数 (max_single_segment_time: 30000)

## 输出格式说明

### 普通模式 (Paraformer/Seaco)
```
00:00:01.000 --> 00:00:03.500  [spk1]   你好，欢迎使用语音识别系统
00:00:04.000 --> 00:00:06.200  [spk2]   谢谢，这个系统很好用
```

### SRT 模式 (Paraformer/Seaco)
```
1
00:00:01,000 --> 00:00:03,500
[spk1]   你好，欢迎使用语音识别系统

2  
00:00:04,000 --> 00:00:06,200
[spk2]   谢谢，这个系统很好用
```

### SenseVoice 带情感标签
```
1
00:00:01,000 --> 00:00:03,500
<|HAPPY|>你好，欢迎使用语音识别系统

2
00:00:04,000 --> 00:00:06,200  
<|Speech|><|NEUTRAL|>谢谢，这个系统很好用
```

## 热词功能使用 (Seaco Paraformer)

在"热词"输入框中输入希望优先识别的词汇，使用空格分隔：

```
语音识别 人工智能 深度学习 神经网络
```

这些词汇在识别过程中会得到优先考虑，提高识别准确率。

## 情感和事件标签 (SenseVoice)

### 情感标签
- `<|HAPPY|>` - 快乐、高兴
- `<|SAD|>` - 悲伤、难过  
- `<|ANGRY|>` - 愤怒、生气
- `<|NEUTRAL|>` - 中性、平静
- `<|FEARFUL|>` - 恐惧、害怕
- `<|DISGUSTED|>` - 厌恶、反感
- `<|SURPRISED|>` - 惊讶、意外

### 事件标签
- `<|Speech|>` - 语音内容
- `<|BGM|>` - 背景音乐
- `<|Applause|>` - 掌声
- `<|Laughter|>` - 笑声
- `<|Cry|>` - 哭声
- `<|Sneeze|>` - 打喷嚏
- `<|Breath|>` - 呼吸声
- `<|Cough|>` - 咳嗽声
- `<|Event_UNK|>` - 未知事件

## 技术架构

### 核心优化
- 🔧 **统一配置**: 单一配置文件，根据模型类型选择section
- 🎯 **智能界面**: 基于配置字典的动态界面更新
- 💾 **优化缓存**: 基于模型类型和参数的智能缓存
- 🔄 **异步处理**: 多线程避免界面冻结
- 📊 **分离处理**: 模型特定的结果处理方法

### 代码结构
```
AsrGui (主窗口)
├── 模型类型选择 (单选按钮组)
├── 动态界面更新 (基于配置字典)
├── 统一配置加载 (单一配置文件，多section)
└── RecognitionWorker (识别线程)
    ├── 模型特定参数设置
    ├── 统一识别流程
    └── 分离的结果处理方法
```

## 故障排除

### 常见问题

1. **模型配置加载失败**
   - 检查 `app/model.conf` 文件是否存在
   - 确认对应的model section是否配置正确
   - 检查模型路径或ID是否有效

2. **界面组件显示异常**
   - 重新选择模型类型以刷新界面
   - 检查PySide6版本兼容性

3. **识别结果异常**
   - 确认选择了正确的模型类型
   - 检查音频文件质量和格式
   - 查看控制台错误信息

### 性能优化建议

1. **配置优化**: 将常用模型路径配置在配置文件中
2. **模型缓存**: 避免频繁切换模型类型
3. **参数调优**: 根据音频特点选择合适的模型类型
4. **硬件优化**: 使用GPU加速识别过程

这个优化后的统一GUI客户端提供了更加简洁、高效和易维护的语音识别功能！ 