from funasr import AutoModel
import os
import argparse
import configparser
import sys
import time
import librosa
import configparser

config = configparser.ConfigParser()
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'model.conf')
config.read(config_path, encoding='utf-8')

def format_timestamp(ms):
    """将毫秒转换为 HH:MM:SS,ms 格式"""
    seconds = int(ms / 1000)  # 整数秒
    milliseconds = int(ms % 1000)  # 毫秒部分
    
    hours = seconds // 3600
    seconds %= 3600
    minutes = seconds // 60
    seconds %= 60
    
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

# 记录脚本开始执行的时间
script_start_time = time.time()

# 添加命令行参数解析
parser = argparse.ArgumentParser(description='使用FunASR进行语音识别并提取时间戳')
parser.add_argument('input_path', type=str, help='输入音频文件的路径')
parser.add_argument('--device', type=str, default="cuda", help='运行设备，可选 cuda 或 cpu')

args = parser.parse_args()

# 从 model.conf 中读取 VAD、PUNC、SPK 模型路径（ASR 模型路径由参数提供）
config = configparser.ConfigParser()
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'model.conf')
config.read(config_path, encoding='utf-8')
try:
    # 仅取每个 section 中的第一个模型路径
    vad_model_path = next(iter(config['vad_models_dir'].values()))
    punc_model_path = next(iter(config['punc_models_dir'].values()))
    spk_model_path = next(iter(config['spk_models_dir'].values()))
except Exception as e:
    print(f"从 model.conf 获取模型路径失败: {e}")
    sys.exit(1)

# 检查输入文件是否存在
if not os.path.exists(args.input_path):
    print(f"错误: 输入文件 '{args.input_path}' 不存在!")
    sys.exit(1)

input_path = args.input_path

# 获取音频时长
try:
    audio_duration = librosa.get_duration(path=input_path)
    print(f"音频时长: {audio_duration:.2f} 秒")
except Exception as e:
    print(f"获取音频时长失败: {e}")
    audio_duration = 0

# 获取输入文件的名称（不含扩展名）
base_name = os.path.splitext(os.path.basename(input_path))[0]
# 创建输出目录（与输入文件相同目录）
output_dir = os.path.dirname(input_path)
# 构建输出文件路径
output_path = os.path.join(output_dir, f"{base_name}.txt")

# 准备模型参数，使用从 config 中读取的路径
model_params = {
    "disable_update": True,
    "device": args.device,
    "model": config['asr_models_dir']['speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch'],
    "vad_model": config['vad_models_dir']['speech_fsmn_vad_zh-cn-16k-common-pytorch'],
    "vad_kwargs": {"max_single_segment_time": 60000},
    "punc_model": config['punc_models_dir']['punc_ct-transformer_zh-cn-common-vocab272727-pytorch'],
    "spk_model": config['spk_models_dir']['speech_campplus_sv_zh-cn_16k-common'],
    "timestamp": True,  # 需要开启时间戳
}

print(f"加载模型参数: {model_params}")
model = AutoModel(**model_params)

print(f"正在处理音频文件: {input_path}")

# 记录模型处理开始时间
model_start_time = time.time()

res = model.generate(
    input=input_path,
    cache={},
    return_raw_text=True,
)

# 记录模型处理结束时间并计算处理时间
model_end_time = time.time()
model_processing_time = model_end_time - model_start_time

print("识别结果结构：", res)  # 打印结果，查看实际的数据结构

# 将结果保存到文件，格式化输出
formatted_results = []  # 存储格式化后的结果

for result in res:
    if 'sentence_info' in result:
        # 使用 sentence_info 中的分句信息
        for sentence in result['sentence_info']:
            # 将说话人编号加1，从1开始，如果ID缺失则使用unknown
            spk_value = sentence.get('spk')
            if spk_value is not None and isinstance(spk_value, int):
                speaker = f"spk{spk_value + 1}"
            else:
                speaker = "spkunknown"  # 使用unknown标记未知说话人
            
            # 去除句尾标点符号
            text = sentence.get('text', '').rstrip(',.。，!！?？')
            start_time = format_timestamp(sentence.get('start', 0))
            end_time = format_timestamp(sentence.get('end', 0))
            
            formatted_line = f"{start_time} --> {end_time}  {speaker}   {text}"
            formatted_results.append(formatted_line)

    elif 'raw_text' in result and 'timestamp' in result:
        # 使用 return_raw_text 细粒度分段信息
        spk_value = result.get('spk')
        if spk_value is not None and isinstance(spk_value, int):
            speaker = f"spk{spk_value + 1}"
        else:
            speaker = "spkunknown"
        pieces = result.get('raw_text', '').split()
        for (start_ms, end_ms), piece in zip(result.get('timestamp', []), pieces):
            start_time = format_timestamp(start_ms)
            end_time = format_timestamp(end_ms)
            text_piece = piece.rstrip(',.。，!！?？')
            formatted_results.append(f"{start_time} --> {end_time}  {speaker}   {text_piece}")

    elif 'timestamp' in result:
        # 如果没有 sentence_info，则使用原来的处理方式
        # 将说话人编号加1，从1开始，如果ID缺失则使用unknown
        spk_value = result.get('spk')
        if spk_value is not None and isinstance(spk_value, int):
            speaker = f"spk{spk_value + 1}"
        else:
            speaker = "spkunknown"  # 使用unknown标记未知说话人
            
        # 去除句尾标点符号
        text = result.get('text', '').rstrip(',.。，!！?？')
        
        for ts in result['timestamp']:
            start_time = format_timestamp(ts[0])
            end_time = format_timestamp(ts[1])
            
            formatted_line = f"{start_time} --> {end_time}  {speaker}   {text}"
            formatted_results.append(formatted_line)

    else:
        # 如果没有时间戳信息，直接添加文本，同样去除句尾标点
        formatted_results.append(result.get('text', '').rstrip(',.。，!！?？'))

# 打印格式化的结果
print("识别结果格式化输出:")
for line in formatted_results:
    print(line)

# 保存到文件
with open(output_path, 'w', encoding='utf-8') as f:
    for line in formatted_results:
        f.write(line + '\n')

print(f"\n识别结果已保存到：{output_path}")

# 计算脚本总执行时间
script_end_time = time.time()
script_execution_time = script_end_time - script_start_time

# 输出时间统计信息
print("\n时间统计信息:")
print(f"脚本总执行时间: {script_execution_time:.2f} 秒")
print(f"音频时长: {audio_duration:.2f} 秒")
print(f"模型处理时间: {model_processing_time:.2f} 秒")
print(f"处理耗时占比: {(model_processing_time/audio_duration)*100:.2f}%")
