from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess
import configparser
import os

config = configparser.ConfigParser()
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
config.read(config_path, encoding="utf-8")

model = AutoModel(
    model=config['asr_models_dir']['speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch'],
    vad_model=config['vad_models_dir']['speech_fsmn_vad_zh-cn-16k-common-pytorch'],
    punc_model=config['punc_models_dir']['punc_ct-transformer_zh-cn-common-vocab272727-pytorch'],
    spk_model=config['spk_models_dir']['speech_campplus_sv_zh-cn_16k-common'],
    disable_update=True
)

res = model.generate(
    input=f"/home/<USER>/音乐/诗朗诵_面朝大海春暖花开.wav",
    cache={},
    hotword="亲人 前程",
    return_raw_text=True,     # 返回按时间戳等长间隔分割的原始识别文本
    # preset_spk_num=2,         # 设置speaker cluster模型的预设speaker数量
    # sentence_timestamp=True,  # 当spk_model未给出时，返回句子级别的信息
)
print(res)
# text = rich_transcription_postprocess(res[0]["text"])
# print(text)