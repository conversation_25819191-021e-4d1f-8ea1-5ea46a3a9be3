import gradio as gr
from funasr import AutoModel
import os
import configparser
import time

# 辅助函数，参考自 demo_timestamp.py

def format_timestamp(ms):
    """将毫秒转换为 HH:MM:SS,ms 格式"""
    seconds = int(ms / 1000)
    milliseconds = int(ms % 1000)
    hours = seconds // 3600
    seconds %= 3600
    minutes = seconds // 60
    seconds %= 60
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

# --- 从 model.conf 加载模型配置 ---
config = configparser.ConfigParser()
# config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'model.conf')
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'model_mac.conf')
if not os.path.exists(config_path):
    raise FileNotFoundError(f"配置文件 'model.conf' 未找到: {config_path}")
config.read(config_path, encoding='utf-8')

# 获取各种模型列表
asr_models = dict(config['asr_models_dir']) if 'asr_models_dir' in config else {}
vad_models = dict(config['vad_models_dir']) if 'vad_models_dir' in config else {}
punc_models = dict(config['punc_models_dir']) if 'punc_models_dir' in config else {}
spk_models = dict(config['spk_models_dir']) if 'spk_models_dir' in config else {}

# 默认模型配置
default_asr = list(asr_models.keys())[0] if asr_models else None
default_vad = list(vad_models.keys())[0] if vad_models else None
default_punc = list(punc_models.keys())[0] if punc_models else None
default_spk = list(spk_models.keys())[0] if spk_models else None

# --- 全局模型缓存 ---
model_cache = {}

def get_model(device, asr_model_name, vad_model_name, punc_model_name, spk_model_name):
    """获取或加载所选模型实例，包含 VAD kwargs"""
    key = f"{device}_{asr_model_name}_{vad_model_name}_{punc_model_name}_{spk_model_name}"
    if key not in model_cache:
        print(f"为配置 {key} 加载模型...")
        asr_path = asr_models.get(asr_model_name)
        vad_path = vad_models.get(vad_model_name)
        punc_path = punc_models.get(punc_model_name)
        spk_path = spk_models.get(spk_model_name)
        if not all([asr_path, vad_path, punc_path, spk_path]):
            missing = []
            if not asr_path: missing.append(f"ASR: {asr_model_name}")
            if not vad_path: missing.append(f"VAD: {vad_model_name}")
            if not punc_path: missing.append(f"PUNC: {punc_model_name}")
            if not spk_path: missing.append(f"SPK: {spk_model_name}")
            raise ValueError(f"配置 {key} 的模型路径缺失: {', '.join(missing)}")
        params = {
            "disable_update": True,
            "device": device,
            "model": asr_path,
            "vad_model": vad_path,
            "vad_kwargs": {"max_single_segment_time": 60000},
            "punc_model": punc_path,
            "spk_model": spk_path,
            "timestamp": True,
        }
        model_cache[key] = AutoModel(**params)
        print(f"配置 {key} 的模型加载完成。")
    return model_cache[key]

# --- Gradio 的主要处理函数 ---
def process_audio(audio_file_path, device, asr_choice, vad_choice, punc_choice, spk_choice, hotword, output_mode, preset_spk_num):
    if not audio_file_path:
        return "请先上传一个音频文件。"
    if not all([asr_choice, vad_choice, punc_choice, spk_choice]):
        return "错误：请确保所有模型（ASR, VAD, PUNC, SPK）都已选择。"

    spk_num_arg = None
    if preset_spk_num is not None and int(preset_spk_num) > 0:
        spk_num_arg = int(preset_spk_num)
        print(f"指定说话人数量: {spk_num_arg}")
    else:
        print("未指定说话人数量，将自动检测。")

    print(f"接收到文件: {audio_file_path}, 设备: {device}, ASR={asr_choice}, VAD={vad_choice}, PUNC={punc_choice}, SPK={spk_choice}, 热词={hotword}, 模式={output_mode}")
    # 记录模型识别开始时间
    t0 = time.time()
    try:
        model_config_id = f"{device}_{asr_choice}_{vad_choice}_{punc_choice}_{spk_choice}"
        if not hasattr(process_audio, 'current_model') or process_audio.current_model != model_config_id:
            print(f"加载新模型组合: {model_config_id}")
            process_audio.model = get_model(device, asr_choice, vad_choice, punc_choice, spk_choice)
            process_audio.current_model = model_config_id
        model = process_audio.model
        print(f"开始处理音频文件: {audio_file_path}")
        gen_kwargs = {"input": audio_file_path, "cache": {}, "return_raw_text": True}
        if spk_num_arg:
            gen_kwargs["preset_spk_num"] = spk_num_arg
        if hotword and hotword.strip():
            gen_kwargs["hotword"] = hotword
        res = model.generate(**gen_kwargs)
        # 记录识别完成时间
        t1 = time.time()
        print(f"模型识别耗时: {t1 - t0:.2f} 秒")
        print("识别完成，开始格式化结果...")

        formatted = []
        idx = 1
        for result in res:
            if 'sentence_info' in result:
                for sent in result['sentence_info']:
                    spk = sent.get('spk')
                    speaker = f"spk{spk+1}" if isinstance(spk, int) else "spkunknown"
                    text = sent.get('text','').rstrip(',.。，!！?？')
                    st = format_timestamp(sent.get('start',0))
                    et = format_timestamp(sent.get('end',0))
                    spk_fmt = f"[{speaker}]"
                    if output_mode == 'timestamp':
                        formatted.append(str(idx)); formatted.append(f"{st} --> {et}"); formatted.append(f"{spk_fmt}   {text}"); formatted.append(""); idx += 1
                    else:  # normal
                        formatted.append(text)
            elif 'raw_text' in result and 'timestamp' in result:
                spk = result.get('spk')
                speaker = f"spk{spk+1}" if isinstance(spk, int) else "spkunknown"
                pieces = result.get('raw_text','').split()
                for (s_ms,e_ms), piece in zip(result.get('timestamp',[]), pieces):
                    st = format_timestamp(s_ms); et = format_timestamp(e_ms)
                    spk_fmt = f"[{speaker}]"; txt = piece.rstrip(',.。，!！?？')
                    if output_mode == 'timestamp':
                        formatted.append(str(idx)); formatted.append(f"{st} --> {et}"); formatted.append(f"{spk_fmt}   {txt}"); formatted.append(""); idx += 1
                    else:  # normal
                        formatted.append(piece)
            elif 'timestamp' in result:
                spk = result.get('spk')
                speaker = f"spk{spk+1}" if isinstance(spk, int) else "spkunknown"
                text = result.get('text','').rstrip(',.。，!！?？')
                for ts in result['timestamp']:
                    st = format_timestamp(ts[0]); et = format_timestamp(ts[1])
                    spk_fmt = f"[{speaker}]"
                    if output_mode == 'timestamp':
                        formatted.append(str(idx)); formatted.append(f"{st} --> {et}"); formatted.append(f"{spk_fmt}   {text}"); formatted.append(""); idx += 1
                    else:  # normal
                        formatted.append(text)
            else:
                txt = result.get('text','').rstrip(',.。，!！?？')
                formatted.append(txt)
        output = "\n".join(formatted)
        print("结果格式化完成。")
        # 更新 Textbox 标签显示耗时
        label = f"识别结果(耗时{t1 - t0:.2f}秒)"
        return output, gr.update(label=label)
    except Exception as e:
        print(f"处理音频时出错: {e}")
        import traceback; traceback.print_exc()
        return f"处理失败: {e}", gr.update(label="识别结果")

# --- Gradio 界面定义 ---
with gr.Blocks(theme=gr.themes.Soft()) as demo:
    gr.Markdown("# 语音识别 (带时间戳、说话人、自定义热词)")
    gr.Markdown("上传音频文件，可选择设备、模型及自定义热词，支持 SRT 输出和指定说话人数。")
    with gr.Row():
        with gr.Column(scale=1):
            audio_input = gr.Audio(type="filepath", label="上传音频文件")
            device_select = gr.Radio(choices=["cuda","cpu"], value="cuda", label="选择设备")
            mode_select = gr.Dropdown(choices=["normal","timestamp"], value="timestamp", label="输出模式")
            hotword_input = gr.Textbox(label="热词 (空格分隔)", placeholder="请输入热词，使用空格分隔", value="")
            with gr.Row():
                preset_spk_num_input = gr.Number(label="指定说话人数 (0=自动)", value=0, minimum=0, step=1)
            with gr.Accordion("模型配置", open=False):
                asr_select = gr.Dropdown(choices=list(asr_models.keys()), value=default_asr, label="ASR 模型")
                vad_select = gr.Dropdown(choices=list(vad_models.keys()), value=default_vad, label="VAD 模型")
                punc_select = gr.Dropdown(choices=list(punc_models.keys()), value=default_punc, label="标点模型")
                spk_select = gr.Dropdown(choices=list(spk_models.keys()), value=default_spk, label="说话人模型")
            submit_button = gr.Button("开始识别")
        with gr.Column(scale=2):
            text_output = gr.Textbox(label="识别结果", lines=50, interactive=False)
    submit_button.click(
        fn=process_audio,
        inputs=[audio_input, device_select, asr_select, vad_select, punc_select, spk_select, hotword_input, mode_select, preset_spk_num_input],
        outputs=[text_output, text_output]
    )

if __name__ == "__main__":
    demo.launch(inbrowser=True, server_port=7861)
