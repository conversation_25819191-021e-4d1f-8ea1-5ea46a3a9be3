from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess
import os
import configparser

config = configparser.ConfigParser()
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
config.read(config_path, encoding="utf-8")


model_params = {
    "disable_update": True,
    "device": "cuda",
    "model": config['asr_models_dir']['sensevoice_small'],
    "vad_model": config['vad_models_dir']['speech_fsmn_vad_zh-cn-16k-common-pytorch'],
    "vad_kwargs": {"max_single_segment_time": 30000},
}

model = AutoModel(**model_params)

# zh with timestamp
res = model.generate(
    input=f"/home/<USER>/音乐/诗朗诵_面朝大海春暖花开.wav",
    cache={},
    language="auto",  #
    # use_itn=True,
    # ban_emo_unk=True,
    batch_size_s=60,
    merge_vad=True,  #
    merge_length_s=15,
    output_timestamp=True,
)
print(res)

# 生成 SRT 格式字幕，保留情感标签和事件标签
import re
res0 = res[0]
raw_text = res0['text']
timestamps = res0['timestamp']
emo_tags = {'<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>'}
event_tags = {'<|Speech|>', '<|BGM|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>', '<|Event_UNK|>'}
parts = raw_text.split('<|zh|>')
segments = []
offset = 0
for part in parts:
    if not part:
        continue
    m = re.match(r'((?:<\|[^|]+\|>)+)', part)
    if m:
        raw = m.group(1)
        raw_tags = re.findall(r'<\|[^|]+\|>', raw)
        tags = ''.join([t for t in raw_tags if t in emo_tags or t in event_tags])
        content = part[len(raw):].strip()
    else:
        tags = ''
        content = part.strip()
    length = len(content)
    if length == 0:
        continue
    start = timestamps[offset][0]
    end = timestamps[offset + length - 1][1]
    segments.append((tags + content, start, end))
    offset += length

def ms2srt(ms):
    h = ms // 3600000
    m = (ms % 3600000) // 60000
    s = (ms % 60000) // 1000
    ms_r = ms % 1000
    return f"{h:02d}:{m:02d}:{s:02d},{ms_r:03d}"

for idx, (text, st, et) in enumerate(segments, 1):
    print(idx)
    print(f"{ms2srt(st)} --> {ms2srt(et)}")
    print(text)
    print()