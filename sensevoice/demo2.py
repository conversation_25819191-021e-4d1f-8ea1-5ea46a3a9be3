from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess
import os
import configparser

config = configparser.ConfigParser()
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
config.read(config_path, encoding="utf-8")


model_params = {
    "disable_update": True,
    "device": "cuda",
    "model": config['asr_models_dir']['sensevoice_small'],
    "vad_model": config['vad_models_dir']['speech_fsmn_vad_zh-cn-16k-common-pytorch'],
    "vad_kwargs": {"max_single_segment_time": 30000},
}

model = AutoModel(**model_params)

res = model.inference(
    input=f"/home/<USER>/音乐/诗朗诵_面朝大海春暖花开.wav",
    cache={},
    language="auto", # "zh", "en", "yue", "ja", "ko", "nospeech"
    use_itn=False,
    ban_emo_unk=False,
    output_timestamp=True,
    batch_size_s=60,
    merge_vad=True,
    merge_length_s=15,
)

# timestamp = res[0]["timestamp"]
text = rich_transcription_postprocess(res[0]["text"])
print(text)
# print(timestamp)