import gradio as gr
from funasr import AutoModel
import os
import configparser
import time
import re

# 时间戳格式化函数
def ms2srt(ms):
    h = ms // 3600000
    m = (ms % 3600000) // 60000
    s = (ms % 60000) // 1000
    ms_r = ms % 1000
    return f"{h:02d}:{m:02d}:{s:02d},{ms_r:03d}"

# 加载模型配置
config = configparser.ConfigParser()
# config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model_mac.conf")
if not os.path.exists(config_path):
    raise FileNotFoundError(f"配置文件 'model.conf' 未找到: {config_path}")
config.read(config_path, encoding="utf-8")

asr_models = dict(config['asr_models_dir']) if 'asr_models_dir' in config else {}
vad_models = dict(config['vad_models_dir']) if 'vad_models_dir' in config else {}

default_asr = list(asr_models.keys())[0] if asr_models else None
default_vad = list(vad_models.keys())[0] if vad_models else None

# 模型实例缓存
model_cache = {}

def get_model(device, asr_name, vad_name):
    key = f"{device}_{asr_name}_{vad_name}"
    if key not in model_cache:
        asr_path = asr_models.get(asr_name)
        vad_path = vad_models.get(vad_name)
        if not all([asr_path, vad_path]):
            missing = []
            if not asr_path: missing.append(f"ASR: {asr_name}")
            if not vad_path: missing.append(f"VAD: {vad_name}")
            raise ValueError(f"模型路径缺失: {', '.join(missing)}")
        params = {
            "disable_update": True,
            "device": device,
            "model": asr_path,
            "vad_model": vad_path,
            "vad_kwargs": {"max_single_segment_time": 30000},
        }
        model_cache[key] = AutoModel(**params)
    return model_cache[key]

# 主处理函数
def process_audio(audio_path, device, asr_choice, vad_choice, output_mode):
    if not audio_path:
        return "请先上传一个音频文件。"
    t0 = time.time()
    model = get_model(device, asr_choice, vad_choice)
    gen_kwargs = {
        "input": audio_path,
        "cache": {},
        "language": "auto",
        "batch_size_s": 60,
        "merge_vad": True,
        "merge_length_s": 15,
        "output_timestamp": output_mode == "timestamp",
    }
    res = model.generate(**gen_kwargs)
    t1 = time.time()
    label = f"识别结果(耗时{t1 - t0:.2f}秒)"
    # 普通模式
    if output_mode == "normal":
        texts = [r.get('text', '').strip() for r in res if r.get('text')]
        return "\n".join(texts), gr.update(label=label)
    # 时间戳模式，构建 SRT 样式
    segments = []
    if res:
        raw = res[0].get('text', '')
        timestamps = res[0].get('timestamp', [])
        emo_tags = {'<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>'}
        event_tags = {'<|Speech|>', '<|BGM|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>', '<|Event_UNK|>'}
        parts = raw.split('<|zh|>')
        offset = 0
        for part in parts:
            if not part:
                continue
            m = re.match(r'((?:<\|[^|]+\|>)+)', part)
            if m:
                tags = ''.join([t for t in re.findall(r'<\|[^|]+\|>', m.group(1)) if t in emo_tags or t in event_tags])
                content = part[len(m.group(1)):].strip()
            else:
                tags = ''
                content = part.strip()
            length = len(content)
            if length == 0:
                continue
            st = timestamps[offset][0]
            et = timestamps[offset + length - 1][1] if offset + length - 1 < len(timestamps) else st
            segments.append((tags + content, st, et))
            offset += length
    # 格式化输出
    out_lines = []
    for idx, (txt, st, et) in enumerate(segments, 1):
        out_lines.append(str(idx))
        out_lines.append(f"{ms2srt(st)} --> {ms2srt(et)}")
        out_lines.append(txt)
        out_lines.append("")
    return "\n".join(out_lines), gr.update(label=label)

# Gradio 界面
with gr.Blocks(theme=gr.themes.Soft()) as demo:
    gr.Markdown("# SenseVoice 语音识别 (带时间戳)")
    gr.Markdown("上传音频，选择设备及模型，支持 SRT 输出。")
    with gr.Row():
        with gr.Column(scale=1):
            audio_in = gr.Audio(type='filepath', label='上传音频文件')
            device_sel = gr.Radio(choices=['cuda', 'cpu'], value='cuda', label='选择设备')
            mode_sel = gr.Dropdown(choices=['normal', 'timestamp'], value='timestamp', label='输出模式')
            asr_sel = gr.Dropdown(choices=list(asr_models.keys()), value=default_asr, label='ASR 模型')
            vad_sel = gr.Dropdown(choices=list(vad_models.keys()), value=default_vad, label='VAD 模型')
            btn = gr.Button('开始识别')
        with gr.Column(scale=2):
            txt_out = gr.Textbox(label='识别结果', lines=50, interactive=False)
    btn.click(
        fn=process_audio,
        inputs=[audio_in, device_sel, asr_sel, vad_sel, mode_sel],
        outputs=[txt_out, txt_out]
    )

if __name__ == '__main__':
    demo.launch(inbrowser=True, server_port=7861)
