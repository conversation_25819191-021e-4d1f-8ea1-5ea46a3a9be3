import sys
import os
import configparser
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QLineEdit, QRadioButton, QButtonGroup, QCheckBox, QSpinBox,
    QGroupBox, QComboBox, QTextEdit, QFileDialog, QFormLayout
)
from PySide6.QtCore import QThread, Signal
import time
import funasr
import re

class AsrGui(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SenseVoice 语音识别客户端")
        self.setGeometry(100, 100, 900, 600)

        # 初始化主窗口组件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局（左右分栏）
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(15)

        # 创建左右面板
        self.left_panel = QWidget()
        self.right_panel = QWidget()
        
        # 设置左右面板的布局
        self.left_layout = QVBoxLayout(self.left_panel)
        self.left_layout.setContentsMargins(5, 10, 5, 10)
        self.right_layout = QVBoxLayout(self.right_panel)
        
        # 将左右面板添加到主布局
        self.main_layout.addWidget(self.left_panel, 40)
        self.main_layout.addWidget(self.right_panel, 60)

        # 模型缓存和配置数据存储
        self.asr_models_config = {}
        self.vad_models_config = {}
        self.model_cache = {}

        # --- 左侧控制面板组件构建 ---
        # 标题和描述
        self.title_label = QLabel("SenseVoice 语音识别 (带时间戳和情感识别)")
        self.title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        
        self.description_label = QLabel("上传音频文件，选择运行设备和模型进行识别。支持情感标签识别、事件检测和 SRT 格式输出。")
        self.description_label.setWordWrap(True)
        
        # 将标题和描述添加到左侧面板
        self.left_layout.addWidget(self.title_label)
        self.left_layout.addWidget(self.description_label)
        self.left_layout.addSpacing(15)

        # 音频文件选择
        self.audio_path_edit = QLineEdit()
        self.audio_path_edit.setPlaceholderText("请选择音频文件路径")
        self.audio_path_edit.setReadOnly(True)
        
        self.browse_button = QPushButton("浏览文件")
        self.browse_button.clicked.connect(self.browse_audio_file)
        
        # 创建文件选择布局
        audio_input_layout = QHBoxLayout()
        audio_input_layout.addWidget(self.audio_path_edit)
        audio_input_layout.addWidget(self.browse_button)
        self.left_layout.addLayout(audio_input_layout)
        self.left_layout.addSpacing(10)

        # 设备选择
        self.device_group_box = QGroupBox("选择设备")
        self.device_cpu_radio = QRadioButton("CPU")
        self.device_cuda_radio = QRadioButton("CUDA")
        self.device_cuda_radio.setChecked(True)  # 默认CUDA
        
        self.device_button_group = QButtonGroup()
        self.device_button_group.addButton(self.device_cpu_radio)
        self.device_button_group.addButton(self.device_cuda_radio)
        
        # 创建设备选择布局
        device_layout = QHBoxLayout(self.device_group_box)
        device_layout.addWidget(self.device_cpu_radio)
        device_layout.addWidget(self.device_cuda_radio)
        device_layout.addStretch()
        self.left_layout.addWidget(self.device_group_box)
        self.left_layout.addSpacing(10)

        # SRT选项
        self.srt_checkbox = QCheckBox("输出 SRT 格式")
        self.srt_checkbox.setChecked(True)
        self.left_layout.addWidget(self.srt_checkbox)
        self.left_layout.addSpacing(10)

        # 模型选择
        self.model_config_group = QGroupBox("模型配置")
        model_config_layout = QFormLayout(self.model_config_group)
        model_config_layout.setVerticalSpacing(15)
        model_config_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)
        
        # 设置模型选择下拉框
        self.asr_model_combo = QComboBox()
        self.asr_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.asr_model_combo.setMinimumContentsLength(20)
        
        self.vad_model_combo = QComboBox()
        self.vad_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.vad_model_combo.setMinimumContentsLength(20)
        
        model_config_layout.addRow("ASR 模型:", self.asr_model_combo)
        model_config_layout.addRow("VAD 模型:", self.vad_model_combo)
        
        self.model_config_group.setMinimumHeight(150)
        self.left_layout.addWidget(self.model_config_group)
        self.left_layout.addSpacing(20)

        # 识别按钮
        self.submit_button = QPushButton("开始识别")
        self.submit_button.setFixedHeight(40)
        self.submit_button.setStyleSheet("font-size: 12pt;")
        self.submit_button.clicked.connect(self.start_recognition)
        self.left_layout.addWidget(self.submit_button)
        self.left_layout.addStretch(1)

        # --- 右侧结果显示面板 ---
        self.result_label = QLabel("识别结果")
        self.result_label.setStyleSheet("font-size: 14pt;")
        
        self.result_text_edit = QTextEdit()
        self.result_text_edit.setReadOnly(True)
        self.result_text_edit.setPlaceholderText("模型配置信息和识别结果将显示在这里...")
        
        # 添加到右侧面板
        self.right_layout.addWidget(self.result_label)
        self.right_layout.addWidget(self.result_text_edit)
        
        # 设置左侧面板的尺寸限制
        from PySide6.QtWidgets import QSizePolicy
        self.left_panel.setMinimumWidth(350)
        self.left_panel.setMaximumWidth(500)
        
        # 右侧面板应可伸展
        self.right_panel.setSizePolicy(
            QSizePolicy.Policy.Expanding,
            QSizePolicy.Policy.Expanding
        )
        
        # 加载模型配置
        self.load_model_config()

    def browse_audio_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择音频文件", "", "音频文件 (*.wav *.mp3 *.flac *.m4a *.pcm)")
        if file_path:
            self.audio_path_edit.setText(file_path)
            self.result_text_edit.append(f"已选择音频文件: {file_path}")

    def load_model_config(self):
        config_paths = [
            # os.path.join(os.getcwd(), "sensevoice", "model_mac.conf"),
            os.path.join(os.getcwd(), "sensevoice", "model.conf")
        ]
        
        config = configparser.ConfigParser()
        loaded_path = None
        for path in config_paths:
            if os.path.exists(path):
                try:
                    config.read(path, encoding='utf-8')
                    loaded_path = path
                    break
                except Exception as e:
                    self.result_text_edit.append(f"读取配置文件 {path} 失败: {e}")
                    return

        if not loaded_path:
            self.result_text_edit.append("错误: 未找到 model.conf 文件。请确保它位于 sensevoice 目录下。")
            return

        self.result_text_edit.append(f"成功从 {loaded_path} 加载模型配置。")

        def populate_combo(combo, section_name, model_dict):
            combo.clear()
            model_dict.clear()
            if section_name in config:
                for name, path_or_id in config[section_name].items():
                    combo.addItem(name)
                    model_dict[name] = path_or_id
                if not model_dict:
                     combo.addItem(f"无模型 (在 {section_name})")
            else:
                self.result_text_edit.append(f"警告: 配置文件中未找到 [{section_name}] 部分。")
                combo.addItem(f"无模型 (无 {section_name})")

        populate_combo(self.asr_model_combo, "asr_models_dir", self.asr_models_config)
        populate_combo(self.vad_model_combo, "vad_models_dir", self.vad_models_config)

    def start_recognition(self):
        audio_file = self.audio_path_edit.text()
        if not audio_file or not os.path.exists(audio_file):
            self.result_text_edit.append("错误: 请先选择一个有效的音频文件。")
            return

        device = "cuda" if self.device_cuda_radio.isChecked() else "cpu"
        
        asr_model_name = self.asr_model_combo.currentText()
        vad_model_name = self.vad_model_combo.currentText()

        asr_model_path = self.asr_models_config.get(asr_model_name)
        vad_model_path = self.vad_models_config.get(vad_model_name)

        if not asr_model_path or "错误:" in asr_model_name or "无模型" in asr_model_name:
            self.result_text_edit.append("错误: 请选择一个有效的 ASR 模型。")
            return
        
        if not vad_model_path or "错误:" in vad_model_name or "无模型" in vad_model_name:
            self.result_text_edit.append("错误: 请选择一个有效的 VAD 模型。")
            return

        output_srt = self.srt_checkbox.isChecked()

        self.result_text_edit.clear()
        self.result_label.setText("识别结果")
        self.result_text_edit.append(f"开始识别: {os.path.basename(audio_file)}")
        self.result_text_edit.append(f"  设备: {device}")
        self.result_text_edit.append(f"  ASR 模型: {asr_model_name}")
        self.result_text_edit.append(f"  VAD 模型: {vad_model_name}")
        self.result_text_edit.append(f"  输出 SRT: {'是' if output_srt else '否'}")
        self.result_text_edit.append("\n")

        self.submit_button.setEnabled(False)
        self.submit_button.setText("正在识别中...")

        self.recognition_thread = RecognitionWorker(
            audio_file, device, 
            asr_model_path, vad_model_path,
            output_srt, self.model_cache
        )
        self.recognition_thread.progress.connect(self.update_progress)
        self.recognition_thread.finished.connect(self.recognition_finished)
        self.recognition_thread.error.connect(self.recognition_error)
        self.recognition_thread.start()

    def update_progress(self, message):
        self.result_text_edit.append(message)

    def recognition_finished(self, result_text, processing_time):
        self.result_text_edit.clear()
        self.result_label.setText(f"识别结果 (耗时: {processing_time:.2f} 秒)")
        self.result_text_edit.append(result_text)
        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")

    def recognition_error(self, error_message):
        self.result_text_edit.append(f"\n--- 错误 ---")
        self.result_text_edit.append(error_message)
        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")

class RecognitionWorker(QThread):
    progress = Signal(str)
    finished = Signal(str, float)
    error = Signal(str)

    def __init__(self, audio_file, device, 
                 asr_model_path, vad_model_path, output_srt, model_cache_dict):
        super().__init__()
        self.audio_file = audio_file
        self.device = device
        self.asr_model_path = asr_model_path
        self.vad_model_path = vad_model_path
        self.output_srt = output_srt
        self.model_cache = model_cache_dict

    def _ms2srt(self, ms):
        """时间戳格式化函数"""
        h = ms // 3600000
        m = (ms % 3600000) // 60000
        s = (ms % 60000) // 1000
        ms_r = ms % 1000
        return f"{h:02d}:{m:02d}:{s:02d},{ms_r:03d}"

    def run(self):
        try:
            self.progress.emit("正在准备模型...")
            model_key = f"{self.device}_{self.asr_model_path}_{self.vad_model_path}"

            if model_key in self.model_cache:
                model = self.model_cache[model_key]
                self.progress.emit(f"从缓存加载模型")
            else:
                self.progress.emit(f"首次加载模型 (可能需要一些时间)")
                model_kwargs = {
                    "disable_update": True,
                    "device": self.device,
                    "model": self.asr_model_path,
                    "vad_model": self.vad_model_path,
                    "vad_kwargs": {"max_single_segment_time": 30000}
                }
                
                model = funasr.AutoModel(**model_kwargs)
                self.model_cache[model_key] = model
                self.progress.emit("模型加载完成.")

            self.progress.emit(f"开始识别音频文件: {os.path.basename(self.audio_file)}")
            
            generate_kwargs = {
                "input": self.audio_file,
                "cache": {},
                "language": "auto",
                "batch_size_s": 60,
                "merge_vad": True,
                "merge_length_s": 15,
                "output_timestamp": self.output_srt
            }

            start_process_time = time.time()
            rec_result = model.generate(**generate_kwargs)
            end_process_time = time.time()
            processing_time = end_process_time - start_process_time
            self.progress.emit("原始识别结果获取完毕, 正在格式化...")
            
            output_lines = []
            
            # 普通模式
            if not self.output_srt:
                texts = [r.get('text', '').strip() for r in rec_result if r.get('text')]
                output_lines = texts
            else:
                # 时间戳模式，构建 SRT 样式
                if rec_result:
                    raw = rec_result[0].get('text', '')
                    timestamps = rec_result[0].get('timestamp', [])
                    
                    # 情感和事件标签
                    emo_tags = {'<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>'}
                    event_tags = {'<|Speech|>', '<|BGM|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>', '<|Event_UNK|>'}
                    
                    parts = raw.split('<|zh|>')
                    offset = 0
                    segments = []
                    
                    for part in parts:
                        if not part:
                            continue
                        m = re.match(r'((?:<\|[^|]+\|>)+)', part)
                        if m:
                            tags = ''.join([t for t in re.findall(r'<\|[^|]+\|>', m.group(1)) if t in emo_tags or t in event_tags])
                            content = part[len(m.group(1)):].strip()
                        else:
                            tags = ''
                            content = part.strip()
                        
                        length = len(content)
                        if length == 0:
                            continue
                        
                        st = timestamps[offset][0] if offset < len(timestamps) else 0
                        et = timestamps[offset + length - 1][1] if offset + length - 1 < len(timestamps) else st
                        segments.append((tags + content, st, et))
                        offset += length
                    
                    # 格式化输出
                    for idx, (txt, st, et) in enumerate(segments, 1):
                        output_lines.append(str(idx))
                        output_lines.append(f"{self._ms2srt(st)} --> {self._ms2srt(et)}")
                        output_lines.append(txt)
                        output_lines.append("")
            
            if not output_lines:
                output_lines = ["识别结果为空。"]
            
            self.finished.emit("\n".join(output_lines), processing_time)

        except Exception as e:
            self.error.emit(f"识别过程中发生错误: {str(e)}\n请检查模型路径、音频文件格式和依赖项是否正确安装。\n错误详情: {type(e).__name__}: {e}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = AsrGui()
    window.show()
    sys.exit(app.exec()) 